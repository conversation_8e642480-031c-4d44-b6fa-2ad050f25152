# Linux服务器部署指南

## 🚀 快速部署

### 1. 准备工作

**服务器要求:**
- Ubuntu 18.04+ 或 CentOS 7+
- 至少 1GB RAM
- 至少 10GB 磁盘空间
- Root权限

**域名准备:**
- 购买域名并解析到服务器IP
- 确保80和443端口开放

### 2. 一键部署步骤

```bash
# 1. 上传所有文件到服务器
scp -r ./* root@your-server-ip:/root/portfolio/

# 2. 登录服务器
ssh root@your-server-ip

# 3. 进入项目目录
cd /root/portfolio

# 4. 修改配置
# 编辑 deploy-server.sh，修改域名
nano deploy-server.sh
# 将 DOMAIN="your-domain.com" 改为您的实际域名

# 编辑 setup-ssl.sh，修改域名和邮箱
nano setup-ssl.sh
# 修改 DOMAIN 和 EMAIL 变量

# 5. 设置脚本权限
chmod +x *.sh

# 6. 执行部署
./deploy-server.sh

# 7. 上传网站文件
./upload-site.sh

# 8. 配置SSL证书
./setup-ssl.sh
```

## 📁 脚本说明

### deploy-server.sh
- **功能**: 初始化服务器环境
- **作用**: 安装Nginx、配置防火墙、创建目录结构
- **使用**: `sudo ./deploy-server.sh`

### upload-site.sh
- **功能**: 上传和更新网站文件
- **作用**: 备份旧版本、上传新文件、设置权限
- **使用**: `sudo ./upload-site.sh`

### setup-ssl.sh
- **功能**: 配置SSL证书
- **作用**: 获取Let's Encrypt证书、设置自动续期
- **使用**: `sudo ./setup-ssl.sh`

### manage-site.sh
- **功能**: 网站管理工具
- **作用**: 统一管理网站的各种操作
- **使用**: `sudo ./manage-site.sh`

## 🔧 日常维护

### 更新网站内容
```bash
# 方法1: 使用管理工具
sudo ./manage-site.sh
# 选择选项1: 部署/更新网站

# 方法2: 直接使用上传脚本
sudo ./upload-site.sh
```

### 查看网站状态
```bash
# 使用管理工具
sudo ./manage-site.sh
# 选择选项2: 查看网站状态

# 或直接查看
systemctl status nginx
```

### 查看访问日志
```bash
# 使用管理工具
sudo ./manage-site.sh
# 选择选项3: 查看访问日志

# 或直接查看
tail -f /var/log/portfolio/access.log
```

### 备份网站
```bash
# 使用管理工具
sudo ./manage-site.sh
# 选择选项4: 备份网站

# 备份文件位置: /var/backups/portfolio/
```

## 📂 目录结构

```
/var/www/portfolio/          # 网站根目录
├── index.html              # 主页
├── protfolio.html          # 作品集页面
├── 个人简历-张一钦.pdf      # 简历文件
├── 原型链接集合.md          # 原型链接
└── ...                     # 其他文件

/var/backups/portfolio/      # 备份目录
├── backup_20241201_120000/  # 自动备份
├── manual_backup_xxx.tar.gz # 手动备份
└── ...

/var/log/portfolio/          # 日志目录
├── access.log              # 访问日志
└── error.log               # 错误日志
```

## 🔒 安全配置

### 防火墙设置
```bash
# Ubuntu (UFW)
ufw allow 'Nginx Full'
ufw allow ssh
ufw enable

# CentOS (Firewalld)
firewall-cmd --permanent --add-service=http
firewall-cmd --permanent --add-service=https
firewall-cmd --reload
```

### SSL证书自动续期
```bash
# 查看续期任务
ls -la /etc/cron.daily/certbot-renew

# 手动测试续期
certbot renew --dry-run

# 手动续期
sudo ./setup-ssl.sh --renew
```

## 📊 监控和优化

### 性能监控
```bash
# 查看系统负载
htop

# 查看磁盘使用
df -h

# 查看内存使用
free -h

# 查看网络连接
netstat -tuln | grep :80
netstat -tuln | grep :443
```

### 日志分析
```bash
# 访问统计
awk '{print $1}' /var/log/portfolio/access.log | sort | uniq -c | sort -nr

# 热门页面
awk '{print $7}' /var/log/portfolio/access.log | sort | uniq -c | sort -nr

# 错误统计
grep "error" /var/log/portfolio/error.log | tail -20
```

## 🛠️ 故障排除

### 常见问题

**1. 网站无法访问**
```bash
# 检查Nginx状态
systemctl status nginx

# 检查配置文件
nginx -t

# 重启Nginx
systemctl restart nginx
```

**2. SSL证书问题**
```bash
# 查看证书状态
certbot certificates

# 重新获取证书
sudo ./setup-ssl.sh

# 检查证书有效期
openssl x509 -in /etc/letsencrypt/live/your-domain.com/cert.pem -text -noout
```

**3. 权限问题**
```bash
# 重新设置权限
chown -R www-data:www-data /var/www/portfolio
find /var/www/portfolio -type d -exec chmod 755 {} \;
find /var/www/portfolio -type f -exec chmod 644 {} \;
```

**4. 磁盘空间不足**
```bash
# 清理日志
sudo ./manage-site.sh
# 选择选项9: 清理缓存和日志

# 手动清理
find /var/log -name "*.log" -mtime +7 -delete
```

## 📈 扩展功能

### 添加访问统计
```bash
# 安装GoAccess
apt install goaccess  # Ubuntu
yum install goaccess  # CentOS

# 生成访问报告
goaccess /var/log/portfolio/access.log -o /var/www/portfolio/stats.html --log-format=COMBINED
```

### 设置邮件通知
```bash
# 安装mailutils
apt install mailutils

# 配置监控脚本
cat > /etc/cron.hourly/site-monitor << 'EOF'
#!/bin/bash
if ! curl -s http://your-domain.com > /dev/null; then
    echo "网站无法访问" | mail -s "网站告警" <EMAIL>
fi
EOF

chmod +x /etc/cron.hourly/site-monitor
```

## 📞 技术支持

如遇到问题，请检查：
1. 服务器日志: `/var/log/portfolio/error.log`
2. Nginx日志: `/var/log/nginx/error.log`
3. 系统日志: `journalctl -u nginx`

或联系：
- 邮箱: <EMAIL>
- 电话: 18062604712
