# 张一钦 - 产品经理简历导航页

这是一个单页面的个人简历导航网站，展示了产品经理的主要作品和项目经验。

## 📁 项目结构

```
├── protfolio.html          # 主页面文件
├── 个人简历-张一钦.pdf      # 完整简历PDF
├── 原型链接合集.docx        # 作品集文档
├── 1. 能源管理系统/         # 能源管理相关项目
├── 2.金山云需求集/          # 云监控项目文档
├── 3.优维科技/              # CMDB和运维项目
├── 4.众邦银行/              # 金融行业运维项目
└── README.md               # 项目说明
```

## 🚀 部署到免费平台

### 1. GitHub Pages 部署

1. **创建GitHub仓库**
   ```bash
   git init
   git add .
   git commit -m "Initial commit: 产品经理简历导航页"
   git branch -M main
   git remote add origin https://github.com/你的用户名/portfolio.git
   git push -u origin main
   ```

2. **启用GitHub Pages**
   - 进入仓库设置 (Settings)
   - 找到 "Pages" 选项
   - Source 选择 "Deploy from a branch"
   - Branch 选择 "main"
   - 点击 Save

3. **访问地址**
   - 网站将在 `https://你的用户名.github.io/portfolio` 可访问
   - 将 `protfolio.html` 重命名为 `index.html` 以作为默认首页

### 2. Vercel 部署

1. **准备代码**
   ```bash
   # 将主文件重命名为 index.html
   mv protfolio.html index.html
   ```

2. **部署步骤**
   - 访问 [vercel.com](https://vercel.com)
   - 使用GitHub账号登录
   - 点击 "New Project"
   - 导入你的GitHub仓库
   - 点击 "Deploy"

3. **自定义域名**
   - 在Vercel项目设置中可以添加自定义域名
   - 免费版提供 `.vercel.app` 子域名

### 3. Netlify 部署

1. **部署方式一：Git连接**
   - 访问 [netlify.com](https://netlify.com)
   - 点击 "New site from Git"
   - 连接GitHub仓库
   - 自动部署

2. **部署方式二：拖拽部署**
   - 将所有文件打包成zip
   - 直接拖拽到Netlify部署区域

## 📝 自定义修改

### 修改个人信息
编辑 `protfolio.html` 文件中的以下部分：

```html
<!-- 个人基本信息 -->
<h1>张一钦</h1>
<p class="title">产品经理 | 云计算与AI应用专家</p>
<div class="contact-info">
    <div class="contact-item">📧 你的邮箱</div>
    <div class="contact-item">📱 你的电话</div>
    <div class="contact-item">📍 你的城市</div>
</div>
```

### 更新项目链接
修改项目卡片中的链接地址：

```html
<a href="你的项目链接" class="project-link" target="_blank">访问项目 →</a>
```

### 替换文件
- 替换 `个人简历-张一钦.pdf` 为你的简历文件
- 替换 `原型链接合集.docx` 为你的作品集文档

## 🎨 样式自定义

### 修改主题色彩
在CSS变量中修改颜色：

```css
:root {
    --primary-color: #3498db;    /* 主色调 */
    --secondary-color: #2c3e50;  /* 次要色 */
    --accent-color: #e74c3c;     /* 强调色 */
}
```

### 添加新的项目分类
复制现有的项目分类HTML结构，修改标题和内容即可。

## 📱 响应式设计

网站已适配移动端，在手机、平板、桌面端都有良好的显示效果。

## 🔗 在简历中使用

部署完成后，可以在简历中添加以下内容：

```
个人作品集网站：https://你的域名
- 包含详细的项目案例和原型设计
- 展示云监控、运维管理、AI应用等领域经验
- 提供在线演示和文档下载
```

## 📞 技术支持

如需帮助或有问题，请联系：
- 邮箱：<EMAIL>
- 电话：18062604712

## 📄 许可证

本项目仅供个人简历展示使用。
