#!/bin/bash

# 个人网站Linux服务器部署脚本
# 作者: 张一钦
# 用途: 在Linux服务器上部署个人简历导航网站

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
SITE_NAME="portfolio"
DOMAIN="your-domain.com"  # 请修改为您的域名
WEB_ROOT="/var/www/${SITE_NAME}"
NGINX_CONF="/etc/nginx/sites-available/${SITE_NAME}"
NGINX_ENABLED="/etc/nginx/sites-enabled/${SITE_NAME}"
BACKUP_DIR="/var/backups/portfolio"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        echo "请使用: sudo $0"
        exit 1
    fi
}

# 检查系统类型
check_system() {
    if [[ -f /etc/debian_version ]]; then
        OS="debian"
        log_info "检测到Debian/Ubuntu系统"
    elif [[ -f /etc/redhat-release ]]; then
        OS="redhat"
        log_info "检测到RedHat/CentOS系统"
    else
        log_error "不支持的操作系统"
        exit 1
    fi
}

# 安装必要软件
install_packages() {
    log_step "安装必要软件包..."
    
    if [[ $OS == "debian" ]]; then
        apt update
        apt install -y nginx git curl wget unzip certbot python3-certbot-nginx
    elif [[ $OS == "redhat" ]]; then
        yum update -y
        yum install -y nginx git curl wget unzip epel-release
        yum install -y certbot python3-certbot-nginx
    fi
    
    log_info "软件包安装完成"
}

# 创建目录结构
create_directories() {
    log_step "创建目录结构..."
    
    mkdir -p $WEB_ROOT
    mkdir -p $BACKUP_DIR
    mkdir -p /var/log/portfolio
    
    log_info "目录结构创建完成"
}

# 配置Nginx
configure_nginx() {
    log_step "配置Nginx..."
    
    # 备份原配置
    if [[ -f $NGINX_CONF ]]; then
        cp $NGINX_CONF "${NGINX_CONF}.backup.$(date +%Y%m%d_%H%M%S)"
    fi
    
    # 创建Nginx配置文件
    cat > $NGINX_CONF << EOF
server {
    listen 80;
    server_name $DOMAIN www.$DOMAIN;
    
    # 重定向到HTTPS
    return 301 https://\$server_name\$request_uri;
}

server {
    listen 443 ssl http2;
    server_name $DOMAIN www.$DOMAIN;
    
    root $WEB_ROOT;
    index index.html protfolio.html;
    
    # SSL配置 (将由certbot自动配置)
    # ssl_certificate /etc/letsencrypt/live/$DOMAIN/fullchain.pem;
    # ssl_certificate_key /etc/letsencrypt/live/$DOMAIN/privkey.pem;
    
    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss;
    
    # 静态文件缓存
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|pdf|docx|md)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # 主页面
    location / {
        try_files \$uri \$uri/ /index.html;
    }
    
    # 访问日志
    access_log /var/log/portfolio/access.log;
    error_log /var/log/portfolio/error.log;
    
    # 隐藏Nginx版本
    server_tokens off;
}
EOF
    
    # 启用站点
    ln -sf $NGINX_CONF $NGINX_ENABLED
    
    # 测试配置
    nginx -t
    
    log_info "Nginx配置完成"
}

# 启动服务
start_services() {
    log_step "启动服务..."
    
    systemctl enable nginx
    systemctl restart nginx
    
    log_info "服务启动完成"
}

# 设置防火墙
setup_firewall() {
    log_step "配置防火墙..."
    
    if command -v ufw &> /dev/null; then
        ufw allow 'Nginx Full'
        ufw allow ssh
        log_info "UFW防火墙配置完成"
    elif command -v firewall-cmd &> /dev/null; then
        firewall-cmd --permanent --add-service=http
        firewall-cmd --permanent --add-service=https
        firewall-cmd --reload
        log_info "Firewalld防火墙配置完成"
    else
        log_warn "未检测到防火墙，请手动配置"
    fi
}

# 主函数
main() {
    log_info "开始部署个人网站..."
    
    check_root
    check_system
    install_packages
    create_directories
    configure_nginx
    start_services
    setup_firewall
    
    log_info "基础环境部署完成！"
    echo ""
    echo "接下来的步骤："
    echo "1. 修改域名配置: 编辑此脚本中的DOMAIN变量"
    echo "2. 上传网站文件: 使用 upload-site.sh 脚本"
    echo "3. 配置SSL证书: 使用 setup-ssl.sh 脚本"
    echo "4. 测试网站访问: http://$DOMAIN"
}

# 执行主函数
main "$@"
