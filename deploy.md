# 快速部署指南

## 🚀 一键部署到各平台

### GitHub Pages 部署命令

```bash
# 1. 初始化Git仓库
git init

# 2. 添加所有文件
git add .

# 3. 提交代码
git commit -m "feat: 添加产品经理简历导航页"

# 4. 添加远程仓库（替换为你的仓库地址）
git remote add origin https://github.com/你的用户名/portfolio.git

# 5. 推送到GitHub
git branch -M main
git push -u origin main

# 6. 在GitHub仓库设置中启用Pages功能
# Settings -> Pages -> Source: Deploy from a branch -> Branch: main
```

### Vercel 部署

```bash
# 1. 安装Vercel CLI（可选）
npm i -g vercel

# 2. 登录Vercel
vercel login

# 3. 部署项目
vercel

# 或者直接在 https://vercel.com 网站上导入GitHub仓库
```

### Netlify 部署

```bash
# 方式1: 拖拽部署
# 1. 将所有文件压缩成zip
# 2. 访问 https://app.netlify.com/drop
# 3. 拖拽zip文件到页面

# 方式2: Git部署
# 1. 推送代码到GitHub（同上）
# 2. 在Netlify连接GitHub仓库
```

## 📝 部署前检查清单

- [ ] 确认个人信息已更新（姓名、联系方式、简介）
- [ ] 确认项目链接都可以正常访问
- [ ] 确认简历PDF文件已上传
- [ ] 确认作品集文档已上传
- [ ] 测试页面在不同设备上的显示效果

## 🔧 常见问题解决

### 1. GitHub Pages 404错误
- 确保主文件名为 `index.html` 或在仓库设置中指定入口文件
- 检查仓库是否为公开状态

### 2. 文件下载链接失效
- 确保PDF和DOCX文件在正确的路径
- 检查文件名是否包含特殊字符

### 3. 外部链接无法访问
- 确认能源管理系统链接是否有效
- 确认智能体链接是否可以公开访问

## 🌐 域名配置

### 自定义域名设置
1. **购买域名**（推荐阿里云、腾讯云、Cloudflare）
2. **DNS配置**
   - GitHub Pages: 添加CNAME记录指向 `你的用户名.github.io`
   - Vercel: 在项目设置中添加域名，按提示配置DNS
   - Netlify: 在站点设置中添加自定义域名

### 免费域名选项
- `.tk`, `.ml`, `.ga` 等免费域名（Freenom）
- GitHub提供的 `.github.io` 子域名
- Vercel提供的 `.vercel.app` 子域名
- Netlify提供的 `.netlify.app` 子域名

## 📊 访问统计

### 添加访问统计（可选）
```html
<!-- Google Analytics -->
<script async src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'GA_MEASUREMENT_ID');
</script>

<!-- 或使用简单的访问计数器 -->
<script async src="https://busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script>
<span id="busuanzi_container_site_pv">
    本站总访问量<span id="busuanzi_value_site_pv"></span>次
</span>
```

## 🔄 更新维护

### 定期更新内容
- 添加新的项目案例
- 更新联系方式
- 优化页面性能
- 修复可能的链接失效

### 版本控制
```bash
# 更新内容后重新部署
git add .
git commit -m "update: 更新项目信息"
git push origin main
```

## 📱 SEO优化建议

```html
<!-- 在<head>中添加SEO标签 -->
<meta name="description" content="张一钦产品经理简历导航页，展示云监控、运维管理、AI应用等领域的产品设计经验">
<meta name="keywords" content="产品经理,云监控,运维管理,AI应用,简历,作品集">
<meta name="author" content="张一钦">

<!-- Open Graph标签 -->
<meta property="og:title" content="张一钦 - 产品经理简历导航">
<meta property="og:description" content="6年技术研发+4年产品管理，专注企业级B端产品">
<meta property="og:type" content="website">
<meta property="og:url" content="https://你的域名">
```
