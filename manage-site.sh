#!/bin/bash

# 网站管理脚本
# 用途: 统一管理个人网站的各种操作

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 配置变量
SITE_NAME="portfolio"
WEB_ROOT="/var/www/${SITE_NAME}"
BACKUP_DIR="/var/backups/portfolio"
LOG_DIR="/var/log/portfolio"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 显示横幅
show_banner() {
    echo -e "${CYAN}"
    echo "=================================================="
    echo "           个人网站管理工具"
    echo "           Portfolio Site Manager"
    echo "=================================================="
    echo -e "${NC}"
}

# 显示主菜单
show_menu() {
    echo ""
    echo "请选择操作:"
    echo "1. 部署/更新网站"
    echo "2. 查看网站状态"
    echo "3. 查看访问日志"
    echo "4. 备份网站"
    echo "5. 恢复网站"
    echo "6. 重启服务"
    echo "7. 配置SSL证书"
    echo "8. 查看系统信息"
    echo "9. 清理缓存和日志"
    echo "0. 退出"
    echo ""
    read -p "请输入选项 (0-9): " choice
}

# 部署/更新网站
deploy_site() {
    log_step "部署/更新网站..."
    
    if [[ -f "./upload-site.sh" ]]; then
        bash ./upload-site.sh
    else
        log_error "未找到 upload-site.sh 脚本"
        return 1
    fi
}

# 查看网站状态
show_status() {
    log_step "网站状态信息..."
    
    echo ""
    echo "=== Nginx状态 ==="
    systemctl status nginx --no-pager -l
    
    echo ""
    echo "=== 网站文件 ==="
    if [[ -d $WEB_ROOT ]]; then
        ls -la $WEB_ROOT
    else
        log_warn "网站目录不存在"
    fi
    
    echo ""
    echo "=== 磁盘使用 ==="
    df -h $WEB_ROOT
    
    echo ""
    echo "=== 内存使用 ==="
    free -h
    
    echo ""
    echo "=== SSL证书状态 ==="
    if command -v certbot &> /dev/null; then
        certbot certificates 2>/dev/null || echo "未配置SSL证书"
    else
        echo "未安装certbot"
    fi
}

# 查看访问日志
show_logs() {
    log_step "查看访问日志..."
    
    echo ""
    echo "请选择日志类型:"
    echo "1. 访问日志 (最近100行)"
    echo "2. 错误日志 (最近100行)"
    echo "3. 实时访问日志"
    echo "4. 访问统计"
    echo ""
    read -p "请选择 (1-4): " log_choice
    
    case $log_choice in
        1)
            if [[ -f "$LOG_DIR/access.log" ]]; then
                tail -100 "$LOG_DIR/access.log"
            else
                log_warn "访问日志文件不存在"
            fi
            ;;
        2)
            if [[ -f "$LOG_DIR/error.log" ]]; then
                tail -100 "$LOG_DIR/error.log"
            else
                log_warn "错误日志文件不存在"
            fi
            ;;
        3)
            if [[ -f "$LOG_DIR/access.log" ]]; then
                echo "按 Ctrl+C 退出实时日志..."
                tail -f "$LOG_DIR/access.log"
            else
                log_warn "访问日志文件不存在"
            fi
            ;;
        4)
            if [[ -f "$LOG_DIR/access.log" ]]; then
                echo "=== 今日访问统计 ==="
                grep "$(date +%d/%b/%Y)" "$LOG_DIR/access.log" | wc -l | xargs echo "今日访问次数:"
                echo ""
                echo "=== 热门页面 ==="
                awk '{print $7}' "$LOG_DIR/access.log" | sort | uniq -c | sort -nr | head -10
                echo ""
                echo "=== 访问IP统计 ==="
                awk '{print $1}' "$LOG_DIR/access.log" | sort | uniq -c | sort -nr | head -10
            else
                log_warn "访问日志文件不存在"
            fi
            ;;
        *)
            log_error "无效选择"
            ;;
    esac
}

# 备份网站
backup_site() {
    log_step "备份网站..."
    
    if [[ ! -d $WEB_ROOT ]]; then
        log_error "网站目录不存在"
        return 1
    fi
    
    BACKUP_NAME="manual_backup_$(date +%Y%m%d_%H%M%S)"
    BACKUP_PATH="$BACKUP_DIR/$BACKUP_NAME"
    
    mkdir -p "$BACKUP_PATH"
    cp -r $WEB_ROOT/* "$BACKUP_PATH/"
    
    # 压缩备份
    cd $BACKUP_DIR
    tar -czf "${BACKUP_NAME}.tar.gz" "$BACKUP_NAME"
    rm -rf "$BACKUP_NAME"
    
    log_info "备份完成: ${BACKUP_PATH}.tar.gz"
}

# 恢复网站
restore_site() {
    log_step "恢复网站..."
    
    echo "可用的备份:"
    ls -la $BACKUP_DIR/*.tar.gz 2>/dev/null || {
        log_warn "没有找到备份文件"
        return 1
    }
    
    echo ""
    read -p "请输入要恢复的备份文件名 (不含路径): " backup_file
    
    if [[ ! -f "$BACKUP_DIR/$backup_file" ]]; then
        log_error "备份文件不存在"
        return 1
    fi
    
    read -p "确定要恢复吗？这将覆盖当前网站 (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "取消恢复"
        return 0
    fi
    
    # 备份当前网站
    backup_site
    
    # 恢复网站
    cd $BACKUP_DIR
    tar -xzf "$backup_file"
    backup_name=$(basename "$backup_file" .tar.gz)
    
    rm -rf $WEB_ROOT/*
    cp -r "$backup_name"/* $WEB_ROOT/
    rm -rf "$backup_name"
    
    # 设置权限
    chown -R www-data:www-data $WEB_ROOT
    find $WEB_ROOT -type d -exec chmod 755 {} \;
    find $WEB_ROOT -type f -exec chmod 644 {} \;
    
    log_info "网站恢复完成"
}

# 重启服务
restart_services() {
    log_step "重启服务..."
    
    echo "请选择要重启的服务:"
    echo "1. 重启Nginx"
    echo "2. 重新加载Nginx配置"
    echo "3. 重启所有相关服务"
    echo ""
    read -p "请选择 (1-3): " service_choice
    
    case $service_choice in
        1)
            systemctl restart nginx
            log_info "Nginx已重启"
            ;;
        2)
            nginx -t && systemctl reload nginx
            log_info "Nginx配置已重新加载"
            ;;
        3)
            systemctl restart nginx
            log_info "所有服务已重启"
            ;;
        *)
            log_error "无效选择"
            ;;
    esac
}

# 配置SSL证书
setup_ssl() {
    log_step "配置SSL证书..."
    
    if [[ -f "./setup-ssl.sh" ]]; then
        bash ./setup-ssl.sh
    else
        log_error "未找到 setup-ssl.sh 脚本"
        return 1
    fi
}

# 查看系统信息
show_system_info() {
    log_step "系统信息..."
    
    echo ""
    echo "=== 系统基本信息 ==="
    uname -a
    echo ""
    echo "=== 系统负载 ==="
    uptime
    echo ""
    echo "=== 磁盘使用 ==="
    df -h
    echo ""
    echo "=== 内存使用 ==="
    free -h
    echo ""
    echo "=== 网络连接 ==="
    ss -tuln | grep :80
    ss -tuln | grep :443
}

# 清理缓存和日志
cleanup_system() {
    log_step "清理系统..."
    
    echo "请选择清理选项:"
    echo "1. 清理旧日志 (保留最近7天)"
    echo "2. 清理旧备份 (保留最近10个)"
    echo "3. 清理系统缓存"
    echo "4. 全部清理"
    echo ""
    read -p "请选择 (1-4): " cleanup_choice
    
    case $cleanup_choice in
        1|4)
            find $LOG_DIR -name "*.log" -mtime +7 -delete 2>/dev/null || true
            log_info "旧日志已清理"
            ;&
        2|4)
            cd $BACKUP_DIR
            ls -t *.tar.gz 2>/dev/null | tail -n +11 | xargs -r rm -f
            log_info "旧备份已清理"
            ;&
        3|4)
            apt-get autoremove -y 2>/dev/null || yum autoremove -y 2>/dev/null || true
            log_info "系统缓存已清理"
            ;;
        *)
            log_error "无效选择"
            ;;
    esac
}

# 检查权限
check_permissions() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        echo "请使用: sudo $0"
        exit 1
    fi
}

# 主函数
main() {
    check_permissions
    show_banner
    
    while true; do
        show_menu
        
        case $choice in
            1) deploy_site ;;
            2) show_status ;;
            3) show_logs ;;
            4) backup_site ;;
            5) restore_site ;;
            6) restart_services ;;
            7) setup_ssl ;;
            8) show_system_info ;;
            9) cleanup_system ;;
            0) 
                log_info "退出管理工具"
                exit 0
                ;;
            *)
                log_error "无效选择，请重新输入"
                ;;
        esac
        
        echo ""
        read -p "按回车键继续..." -r
    done
}

# 执行主函数
main "$@"
