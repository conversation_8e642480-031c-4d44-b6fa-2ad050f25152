#!/bin/bash

# 网站监控脚本
# 用途: 监控网站状态，自动处理常见问题

set -e

# 配置变量
DOMAIN="your-domain.com"  # 请修改为您的域名
EMAIL="<EMAIL>"  # 请修改为您的邮箱
WEB_ROOT="/var/www/portfolio"
LOG_DIR="/var/log/portfolio"
MONITOR_LOG="/var/log/portfolio/monitor.log"

# 创建监控日志
mkdir -p $LOG_DIR
touch $MONITOR_LOG

# 日志函数
log_monitor() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> $MONITOR_LOG
}

# 检查网站可访问性
check_website() {
    local url="$1"
    local timeout=10
    
    if curl -s --max-time $timeout "$url" > /dev/null; then
        return 0
    else
        return 1
    fi
}

# 检查SSL证书
check_ssl_certificate() {
    local domain="$1"
    local days_before_expiry=30
    
    if ! command -v openssl &> /dev/null; then
        return 1
    fi
    
    local expiry_date=$(echo | openssl s_client -servername $domain -connect $domain:443 2>/dev/null | openssl x509 -noout -dates | grep notAfter | cut -d= -f2)
    local expiry_epoch=$(date -d "$expiry_date" +%s)
    local current_epoch=$(date +%s)
    local days_until_expiry=$(( (expiry_epoch - current_epoch) / 86400 ))
    
    if [ $days_until_expiry -lt $days_before_expiry ]; then
        return 1
    else
        return 0
    fi
}

# 检查磁盘空间
check_disk_space() {
    local threshold=90
    local usage=$(df $WEB_ROOT | awk 'NR==2 {print $5}' | sed 's/%//')
    
    if [ $usage -gt $threshold ]; then
        return 1
    else
        return 0
    fi
}

# 检查内存使用
check_memory() {
    local threshold=90
    local usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    
    if [ $usage -gt $threshold ]; then
        return 1
    else
        return 0
    fi
}

# 检查Nginx状态
check_nginx() {
    if systemctl is-active --quiet nginx; then
        return 0
    else
        return 1
    fi
}

# 发送告警邮件
send_alert() {
    local subject="$1"
    local message="$2"
    
    if command -v mail &> /dev/null; then
        echo "$message" | mail -s "$subject" "$EMAIL"
        log_monitor "告警邮件已发送: $subject"
    else
        log_monitor "无法发送邮件，请安装mailutils"
    fi
}

# 重启Nginx
restart_nginx() {
    log_monitor "尝试重启Nginx..."
    
    if systemctl restart nginx; then
        log_monitor "Nginx重启成功"
        return 0
    else
        log_monitor "Nginx重启失败"
        return 1
    fi
}

# 清理日志文件
cleanup_logs() {
    log_monitor "清理旧日志文件..."
    
    # 清理7天前的日志
    find $LOG_DIR -name "*.log" -mtime +7 -delete 2>/dev/null || true
    
    # 如果日志文件过大，进行轮转
    for log_file in "$LOG_DIR"/*.log; do
        if [ -f "$log_file" ] && [ $(stat -f%z "$log_file" 2>/dev/null || stat -c%s "$log_file") -gt 104857600 ]; then  # 100MB
            mv "$log_file" "${log_file}.old"
            touch "$log_file"
            chown www-data:www-data "$log_file"
        fi
    done
    
    log_monitor "日志清理完成"
}

# 主监控函数
main_monitor() {
    log_monitor "开始监控检查..."
    
    local alerts=()
    
    # 检查网站HTTP访问
    if ! check_website "http://$DOMAIN"; then
        alerts+=("网站HTTP访问失败")
        log_monitor "警告: 网站HTTP访问失败"
        
        # 尝试重启Nginx
        if ! check_nginx; then
            restart_nginx
        fi
    fi
    
    # 检查网站HTTPS访问
    if ! check_website "https://$DOMAIN"; then
        alerts+=("网站HTTPS访问失败")
        log_monitor "警告: 网站HTTPS访问失败"
    fi
    
    # 检查SSL证书
    if ! check_ssl_certificate "$DOMAIN"; then
        alerts+=("SSL证书即将过期或无效")
        log_monitor "警告: SSL证书问题"
    fi
    
    # 检查磁盘空间
    if ! check_disk_space; then
        alerts+=("磁盘空间不足")
        log_monitor "警告: 磁盘空间不足"
        cleanup_logs
    fi
    
    # 检查内存使用
    if ! check_memory; then
        alerts+=("内存使用率过高")
        log_monitor "警告: 内存使用率过高"
    fi
    
    # 检查Nginx状态
    if ! check_nginx; then
        alerts+=("Nginx服务未运行")
        log_monitor "错误: Nginx服务未运行"
        restart_nginx
    fi
    
    # 发送告警
    if [ ${#alerts[@]} -gt 0 ]; then
        local alert_message="检测到以下问题:\n"
        for alert in "${alerts[@]}"; do
            alert_message+="\n- $alert"
        done
        alert_message+="\n\n请及时处理。\n\n监控时间: $(date)"
        
        send_alert "网站监控告警 - $DOMAIN" "$alert_message"
    else
        log_monitor "所有检查正常"
    fi
    
    log_monitor "监控检查完成"
}

# 生成监控报告
generate_report() {
    local report_file="/tmp/site_monitor_report.txt"
    
    cat > $report_file << EOF
网站监控报告
生成时间: $(date)
域名: $DOMAIN

=== 系统状态 ===
$(uptime)

=== 磁盘使用 ===
$(df -h $WEB_ROOT)

=== 内存使用 ===
$(free -h)

=== Nginx状态 ===
$(systemctl status nginx --no-pager -l)

=== 最近访问日志 ===
$(tail -20 $LOG_DIR/access.log 2>/dev/null || echo "无访问日志")

=== 最近错误日志 ===
$(tail -10 $LOG_DIR/error.log 2>/dev/null || echo "无错误日志")

=== 监控日志 ===
$(tail -20 $MONITOR_LOG 2>/dev/null || echo "无监控日志")
EOF
    
    echo $report_file
}

# 安装监控任务
install_cron() {
    log_monitor "安装监控定时任务..."
    
    # 创建监控脚本的cron任务
    cat > /etc/cron.d/portfolio-monitor << EOF
# 每5分钟检查一次网站状态
*/5 * * * * root $(realpath $0) --check

# 每天凌晨2点生成监控报告
0 2 * * * root $(realpath $0) --report

# 每周日凌晨3点清理日志
0 3 * * 0 root $(realpath $0) --cleanup
EOF
    
    log_monitor "监控定时任务安装完成"
}

# 显示帮助信息
show_help() {
    echo "网站监控脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 --check          # 执行监控检查"
    echo "  $0 --report         # 生成监控报告"
    echo "  $0 --cleanup        # 清理日志文件"
    echo "  $0 --install        # 安装定时任务"
    echo "  $0 --status         # 查看监控状态"
    echo "  $0 --help           # 显示帮助信息"
    echo ""
    echo "配置文件位置:"
    echo "  监控日志: $MONITOR_LOG"
    echo "  网站目录: $WEB_ROOT"
    echo "  日志目录: $LOG_DIR"
}

# 查看监控状态
show_status() {
    echo "=== 监控状态 ==="
    echo "域名: $DOMAIN"
    echo "监控日志: $MONITOR_LOG"
    echo ""
    
    if [ -f "$MONITOR_LOG" ]; then
        echo "最近的监控记录:"
        tail -10 "$MONITOR_LOG"
    else
        echo "暂无监控记录"
    fi
    
    echo ""
    echo "定时任务状态:"
    if [ -f "/etc/cron.d/portfolio-monitor" ]; then
        echo "✓ 监控定时任务已安装"
        cat /etc/cron.d/portfolio-monitor
    else
        echo "✗ 监控定时任务未安装"
    fi
}

# 处理命令行参数
case "${1:-}" in
    --check)
        main_monitor
        ;;
    --report)
        report_file=$(generate_report)
        echo "监控报告已生成: $report_file"
        if command -v mail &> /dev/null && [ -n "$EMAIL" ]; then
            mail -s "网站监控报告 - $DOMAIN" "$EMAIL" < "$report_file"
            echo "报告已发送到: $EMAIL"
        fi
        ;;
    --cleanup)
        cleanup_logs
        ;;
    --install)
        if [[ $EUID -ne 0 ]]; then
            echo "安装定时任务需要root权限"
            exit 1
        fi
        install_cron
        ;;
    --status)
        show_status
        ;;
    --help|-h)
        show_help
        ;;
    "")
        echo "请指定操作参数，使用 --help 查看帮助"
        exit 1
        ;;
    *)
        echo "未知参数: $1"
        show_help
        exit 1
        ;;
esac
