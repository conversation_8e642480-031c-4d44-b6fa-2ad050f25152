<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>张一钦 - 产品经理作品集</title>
    <style>
        :root {
            --primary-color: #3498db;
            --secondary-color: #2c3e50;
            --accent-color: #e74c3c;
            --light-color: #ecf0f1;
            --dark-color: #2c3e50;
            --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            background-color: #f9f9f9;
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 2rem 0;
            text-align: center;
            box-shadow: var(--shadow);
        }
        
        .profile {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 1rem;
        }
        
        .avatar {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            border: 4px solid white;
            background-color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: var(--primary-color);
            box-shadow: var(--shadow);
        }
        
        h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }
        
        .title {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .contact-info {
            display: flex;
            flex-wrap: wrap;
            gap: 1.5rem;
            justify-content: center;
            margin-top: 1rem;
        }
        
        .contact-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .main-content {
            padding: 3rem 0;
        }
        
        section {
            background: white;
            border-radius: 10px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow);
        }
        
        h2 {
            color: var(--secondary-color);
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--primary-color);
            display: inline-block;
        }
        
        .intro {
            font-size: 1.1rem;
            margin-bottom: 1.5rem;
        }
        
        .btn {
            display: inline-block;
            background-color: var(--primary-color);
            color: white;
            padding: 0.8rem 1.5rem;
            border-radius: 5px;
            text-decoration: none;
            font-weight: 600;
            transition: var(--transition);
            border: none;
            cursor: pointer;
        }
        
        .btn:hover {
            background-color: var(--secondary-color);
            transform: translateY(-3px);
        }
        
        .btn-download {
            background-color: var(--accent-color);
            margin-right: 1rem;
        }
        
        .projects-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 1.5rem;
        }
        
        .project-card {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            transition: var(--transition);
            box-shadow: 0 3px 5px rgba(0, 0, 0, 0.1);
            border: 1px solid #eee;
        }
        
        .project-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
        }
        
        .project-content {
            padding: 1.5rem;
        }
        
        .project-title {
            font-size: 1.2rem;
            margin-bottom: 0.5rem;
            color: var(--secondary-color);
        }
        
        .project-desc {
            color: #666;
            margin-bottom: 1rem;
            font-size: 0.95rem;
        }
        
        .project-link {
            display: inline-block;
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 600;
            font-size: 0.9rem;
        }
        
        .project-link:hover {
            text-decoration: underline;
        }
        
        .skills-container {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            margin-top: 1.5rem;
        }
        
        .skill-category {
            flex: 1;
            min-width: 250px;
        }
        
        .skill-category h3 {
            margin-bottom: 1rem;
            color: var(--secondary-color);
        }
        
        .skill-list {
            list-style-type: none;
        }
        
        .skill-list li {
            background-color: var(--light-color);
            margin-bottom: 0.8rem;
            padding: 0.8rem;
            border-radius: 5px;
            border-left: 4px solid var(--primary-color);
        }
        
        footer {
            background-color: var(--dark-color);
            color: white;
            text-align: center;
            padding: 2rem 0;
            margin-top: 2rem;
        }
        
        @media (max-width: 768px) {
            .projects-grid {
                grid-template-columns: 1fr;
            }
            
            .contact-info {
                flex-direction: column;
                align-items: center;
                gap: 0.8rem;
            }
            
            .avatar {
                width: 120px;
                height: 120px;
                font-size: 2rem;
            }
            
            h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="profile">
                <div class="avatar">张</div>
                <h1>张一钦</h1>
                <p class="title">产品经理 | 云计算与AI应用专家</p>
                <div class="contact-info">
                    <div class="contact-item">📧 <EMAIL></div>
                    <div class="contact-item">📱 18062604712</div>
                    <div class="contact-item">📍 武汉</div>
                </div>
            </div>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <section id="about">
                <h2>个人简介</h2>
                <p class="intro">
                    6年技术研发与4年产品管理复合背景的产品人，专注于企业级B端产品，尤其在云计算与智能运维领域积累了丰富的实践经验。
                    擅长通过用户访谈、数据分析等方式挖掘客户核心痛点，将业务需求转化为可落地的产品方案。
                </p>
                <a href="个人简历-张一钦.pdf" class="btn btn-download" download>下载简历(PDF)</a>
                <a href="原型链接合集.docx" class="btn" download>下载作品集(DOCX)</a>
            </section>

            <section id="projects">
                <h2>项目作品集</h2>
                <div class="projects-grid">
                    <div class="project-card">
                        <div class="project-content">
                            <h3 class="project-title">能源管理系统</h3>
                            <p class="project-desc">基于AI生成的前端演示界面，包含虚拟电厂、负荷控制、能效管理等模块</p>
                            <a href="http://118.178.228.37:3003/" class="project-link" target="_blank">访问系统 →</a>
                        </div>
                    </div>
                    
                    <div class="project-card">
                        <div class="project-content">
                            <h3 class="project-title">云监控相关系统</h3>
                            <p class="project-desc">GPU事件管理、EPC事件收敛、IB网络监控、RDMA网络监控等原型设计</p>
                            <a href="原型链接合集.docx" class="project-link" download>查看所有原型 →</a>
                        </div>
                    </div>
                    
                    <div class="project-card">
                        <div class="project-content">
                            <h3 class="project-title">内部监控系统</h3>
                            <p class="project-desc">Pingmesh探测、SLI指标优化、事件中心管理、北斗事件告警等原型</p>
                            <a href="原型链接合集.docx" class="project-link" download>查看所有原型 →</a>
                        </div>
                    </div>
                    
                    <div class="project-card">
                        <div class="project-content">
                            <h3 class="project-title">运维系统</h3>
                            <p class="project-desc">服务可观测项目、报警2.0告警升级、手机端告警快速屏蔽等原型</p>
                            <a href="原型链接合集.docx" class="project-link" download>查看所有原型 →</a>
                        </div>
                    </div>
                    
                    <div class="project-card">
                        <div class="project-content">
                            <h3 class="project-title">AI智能体应用</h3>
                            <p class="project-desc">医疗问诊助手、企业办公助手、医疗分诊助手、滴滴计费解答等AI应用</p>
                            <a href="https://www.coze.cn/space/7457055420545400858" class="project-link" target="_blank">查看智能体 →</a>
                        </div>
                    </div>
                </div>
            </section>

            <section id="skills">
                <h2>专业技能</h2>
                <div class="skills-container">
                    <div class="skill-category">
                        <h3>产品管理</h3>
                        <ul class="skill-list">
                            <li>需求挖掘与分析（用户访谈、数据分析、竞品分析）</li>
                            <li>产品原型设计（Axure, MasterGo）</li>
                            <li>KANO模型应用与MVP实践</li>
                            <li>敏捷项目管理（Scrum/Kanban）</li>
                        </ul>
                    </div>
                    
                    <div class="skill-category">
                        <h3>AI应用能力</h3>
                        <ul class="skill-list">
                            <li>大语言模型(LLM)应用实践</li>
                            <li>AI产品功能设计与落地</li>
                            <li>机器学习基础概念理解</li>
                        </ul>
                    </div>
                    
                    <div class="skill-category">
                        <h3>技术能力</h3>
                        <ul class="skill-list">
                            <li>Linux系统与Shell/Python脚本</li>
                            <li>监控体系建设（Zabbix, Prometheus, ELK）</li>
                            <li>CI/CD流程设计（Jenkins、蓝盾）</li>
                            <li>容器化基础（Docker）与云计算</li>
                        </ul>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <footer>
        <div class="container">
            <p>© 2025 张一钦 - 产品经理作品集</p>
            <p>电话: 18062604712 | 邮箱: <EMAIL></p>
        </div>
    </footer>

    <script>
        // 简单的交互效果
        document.addEventListener('DOMContentLoaded', function() {
            const projectCards = document.querySelectorAll('.project-card');
            
            projectCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
            
            // 平滑滚动到锚点
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function(e) {
                    e.preventDefault();
                    const targetId = this.getAttribute('href');
                    if (targetId !== '#') {
                        document.querySelector(targetId).scrollIntoView({
                            behavior: 'smooth'
                        });
                    }
                });
            });
        });
    </script>
</body>
</html>