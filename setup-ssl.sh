#!/bin/bash

# SSL证书配置脚本
# 用途: 为个人网站配置Let's Encrypt SSL证书

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置变量
DOMAIN="your-domain.com"  # 请修改为您的域名
EMAIL="<EMAIL>"  # 请修改为您的邮箱

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        echo "请使用: sudo $0"
        exit 1
    fi
}

# 检查域名配置
check_domain() {
    log_step "检查域名配置..."
    
    if [[ "$DOMAIN" == "your-domain.com" ]]; then
        log_error "请先修改脚本中的DOMAIN变量为您的实际域名"
        exit 1
    fi
    
    if [[ "$EMAIL" == "<EMAIL>" ]]; then
        log_error "请先修改脚本中的EMAIL变量为您的实际邮箱"
        exit 1
    fi
    
    # 检查域名解析
    if ! nslookup $DOMAIN > /dev/null 2>&1; then
        log_warn "域名 $DOMAIN 可能未正确解析到此服务器"
        read -p "是否继续？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
    
    log_info "域名检查完成"
}

# 检查Nginx状态
check_nginx() {
    log_step "检查Nginx状态..."
    
    if ! systemctl is-active --quiet nginx; then
        log_error "Nginx未运行，请先启动Nginx"
        exit 1
    fi
    
    if ! nginx -t; then
        log_error "Nginx配置有误，请检查配置文件"
        exit 1
    fi
    
    log_info "Nginx状态正常"
}

# 获取SSL证书
obtain_certificate() {
    log_step "获取SSL证书..."
    
    # 使用certbot获取证书
    certbot --nginx \
        --non-interactive \
        --agree-tos \
        --email $EMAIL \
        --domains $DOMAIN,www.$DOMAIN \
        --redirect
    
    log_info "SSL证书获取完成"
}

# 设置自动续期
setup_auto_renewal() {
    log_step "设置证书自动续期..."
    
    # 创建续期脚本
    cat > /etc/cron.daily/certbot-renew << 'EOF'
#!/bin/bash
# SSL证书自动续期脚本

/usr/bin/certbot renew --quiet --no-self-upgrade

# 如果证书更新了，重新加载Nginx
if [ $? -eq 0 ]; then
    /bin/systemctl reload nginx
fi
EOF
    
    chmod +x /etc/cron.daily/certbot-renew
    
    # 测试续期
    certbot renew --dry-run
    
    log_info "自动续期设置完成"
}

# 优化SSL配置
optimize_ssl_config() {
    log_step "优化SSL配置..."
    
    # 创建SSL配置文件
    cat > /etc/nginx/snippets/ssl-params.conf << 'EOF'
# SSL配置优化
ssl_protocols TLSv1.2 TLSv1.3;
ssl_prefer_server_ciphers on;
ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-SHA384;
ssl_ecdh_curve secp384r1;
ssl_session_timeout 10m;
ssl_session_cache shared:SSL:10m;
ssl_session_tickets off;
ssl_stapling on;
ssl_stapling_verify on;
resolver ******* ******* valid=300s;
resolver_timeout 5s;

# 安全头
add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";
add_header X-Frame-Options DENY;
add_header X-Content-Type-Options nosniff;
add_header X-XSS-Protection "1; mode=block";
EOF
    
    log_info "SSL配置优化完成"
}

# 测试SSL配置
test_ssl() {
    log_step "测试SSL配置..."
    
    # 重新加载Nginx
    nginx -t && systemctl reload nginx
    
    # 测试HTTPS访问
    if curl -s -I https://$DOMAIN | grep -q "200 OK"; then
        log_info "✓ HTTPS访问正常"
    else
        log_warn "HTTPS访问可能有问题，请手动检查"
    fi
    
    # 显示证书信息
    echo ""
    echo "证书信息:"
    certbot certificates
}

# 主函数
main() {
    log_info "开始配置SSL证书..."
    
    check_root
    check_domain
    check_nginx
    obtain_certificate
    setup_auto_renewal
    optimize_ssl_config
    test_ssl
    
    log_info "SSL配置完成！"
    echo ""
    echo "您的网站现在支持HTTPS访问:"
    echo "- https://$DOMAIN"
    echo "- https://www.$DOMAIN"
    echo ""
    echo "证书将自动续期，无需手动操作。"
}

# 显示帮助信息
show_help() {
    echo "SSL证书配置脚本"
    echo ""
    echo "使用方法:"
    echo "  $0                    # 配置SSL证书"
    echo "  $0 --help           # 显示帮助信息"
    echo "  $0 --renew          # 手动续期证书"
    echo "  $0 --status         # 查看证书状态"
    echo ""
    echo "注意事项:"
    echo "1. 请先修改脚本中的DOMAIN和EMAIL变量"
    echo "2. 确保域名已正确解析到此服务器"
    echo "3. 确保Nginx已正确配置并运行"
}

# 处理命令行参数
case "${1:-}" in
    --help|-h)
        show_help
        exit 0
        ;;
    --renew)
        check_root
        log_info "手动续期证书..."
        certbot renew
        systemctl reload nginx
        log_info "证书续期完成"
        exit 0
        ;;
    --status)
        log_info "证书状态:"
        certbot certificates
        exit 0
        ;;
    "")
        main "$@"
        ;;
    *)
        log_error "未知参数: $1"
        show_help
        exit 1
        ;;
esac
