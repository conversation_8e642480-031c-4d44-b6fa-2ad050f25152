#!/bin/bash

# 网站文件上传和更新脚本
# 用途: 上传和更新个人网站文件

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置变量
SITE_NAME="portfolio"
WEB_ROOT="/var/www/${SITE_NAME}"
BACKUP_DIR="/var/backups/portfolio"
SOURCE_DIR="$(pwd)"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        echo "请使用: sudo $0"
        exit 1
    fi
}

# 备份当前网站
backup_current_site() {
    log_step "备份当前网站..."
    
    if [[ -d $WEB_ROOT ]]; then
        BACKUP_NAME="backup_$(date +%Y%m%d_%H%M%S)"
        mkdir -p "$BACKUP_DIR/$BACKUP_NAME"
        cp -r $WEB_ROOT/* "$BACKUP_DIR/$BACKUP_NAME/" 2>/dev/null || true
        log_info "备份完成: $BACKUP_DIR/$BACKUP_NAME"
    else
        log_warn "网站目录不存在，跳过备份"
    fi
}

# 验证源文件
validate_source_files() {
    log_step "验证源文件..."
    
    required_files=("index.html" "protfolio.html")
    
    for file in "${required_files[@]}"; do
        if [[ ! -f "$SOURCE_DIR/$file" ]]; then
            log_error "缺少必要文件: $file"
            exit 1
        fi
    done
    
    log_info "源文件验证通过"
}

# 上传网站文件
upload_files() {
    log_step "上传网站文件..."
    
    # 创建临时目录
    TEMP_DIR="/tmp/portfolio_upload"
    rm -rf $TEMP_DIR
    mkdir -p $TEMP_DIR
    
    # 复制文件到临时目录
    cp -r "$SOURCE_DIR"/* $TEMP_DIR/
    
    # 排除不需要的文件
    exclude_patterns=(
        "*.sh"
        "*.bat"
        ".git*"
        "node_modules"
        "*.log"
        "*.tmp"
        "deploy*"
        "upload*"
        "setup*"
    )
    
    for pattern in "${exclude_patterns[@]}"; do
        find $TEMP_DIR -name "$pattern" -exec rm -rf {} + 2>/dev/null || true
    done
    
    # 确保目标目录存在
    mkdir -p $WEB_ROOT
    
    # 复制文件到网站目录
    cp -r $TEMP_DIR/* $WEB_ROOT/
    
    # 设置正确的权限
    chown -R www-data:www-data $WEB_ROOT
    find $WEB_ROOT -type d -exec chmod 755 {} \;
    find $WEB_ROOT -type f -exec chmod 644 {} \;
    
    # 清理临时目录
    rm -rf $TEMP_DIR
    
    log_info "文件上传完成"
}

# 验证网站
validate_site() {
    log_step "验证网站..."
    
    # 检查关键文件
    if [[ -f "$WEB_ROOT/index.html" ]]; then
        log_info "✓ index.html 存在"
    else
        log_error "✗ index.html 不存在"
        exit 1
    fi
    
    # 检查Nginx配置
    nginx -t
    log_info "✓ Nginx配置有效"
    
    # 重新加载Nginx
    systemctl reload nginx
    log_info "✓ Nginx已重新加载"
}

# 显示网站信息
show_site_info() {
    log_step "网站信息..."
    
    echo ""
    echo "网站根目录: $WEB_ROOT"
    echo "备份目录: $BACKUP_DIR"
    echo ""
    echo "网站文件列表:"
    ls -la $WEB_ROOT
    echo ""
    echo "最近的备份:"
    ls -la $BACKUP_DIR | tail -5
}

# 清理旧备份
cleanup_old_backups() {
    log_step "清理旧备份..."
    
    # 保留最近10个备份
    cd $BACKUP_DIR
    ls -t | tail -n +11 | xargs -r rm -rf
    
    log_info "旧备份清理完成"
}

# 主函数
main() {
    log_info "开始上传网站文件..."
    
    check_root
    validate_source_files
    backup_current_site
    upload_files
    validate_site
    cleanup_old_backups
    show_site_info
    
    log_info "网站更新完成！"
    echo ""
    echo "您可以通过以下方式访问网站:"
    echo "- HTTP: http://your-domain.com"
    echo "- HTTPS: https://your-domain.com (如果已配置SSL)"
}

# 如果直接运行此脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
