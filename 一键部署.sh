#!/bin/bash

# 一键部署脚本
# 用途: 自动化部署个人网站到Linux服务器

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 显示横幅
show_banner() {
    clear
    echo -e "${CYAN}"
    echo "========================================================"
    echo "           个人网站一键部署脚本"
    echo "           Portfolio Auto Deploy Script"
    echo "========================================================"
    echo -e "${NC}"
}

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查系统要求
check_requirements() {
    log_step "检查系统要求..."
    
    # 检查是否为root用户
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        echo "请使用: sudo $0"
        exit 1
    fi
    
    # 检查系统类型
    if [[ -f /etc/debian_version ]]; then
        OS="debian"
        log_info "检测到Debian/Ubuntu系统"
    elif [[ -f /etc/redhat-release ]]; then
        OS="redhat"
        log_info "检测到RedHat/CentOS系统"
    else
        log_error "不支持的操作系统"
        exit 1
    fi
    
    # 检查网络连接
    if ! ping -c 1 google.com &> /dev/null; then
        log_warn "网络连接可能有问题，请检查网络设置"
    fi
    
    log_info "系统要求检查完成"
}

# 收集配置信息
collect_config() {
    log_step "收集配置信息..."
    
    echo ""
    echo "请提供以下配置信息:"
    echo ""
    
    # 域名配置
    while true; do
        read -p "请输入您的域名 (例: example.com): " DOMAIN
        if [[ -n "$DOMAIN" && "$DOMAIN" != "your-domain.com" ]]; then
            break
        else
            log_error "请输入有效的域名"
        fi
    done
    
    # 邮箱配置
    while true; do
        read -p "请输入您的邮箱 (用于SSL证书和告警): " EMAIL
        if [[ "$EMAIL" =~ ^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$ ]]; then
            break
        else
            log_error "请输入有效的邮箱地址"
        fi
    done
    
    # 确认配置
    echo ""
    echo "配置信息确认:"
    echo "域名: $DOMAIN"
    echo "邮箱: $EMAIL"
    echo ""
    read -p "确认配置正确吗？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "请重新运行脚本"
        exit 0
    fi
    
    log_info "配置信息收集完成"
}

# 更新配置文件
update_config_files() {
    log_step "更新配置文件..."
    
    # 更新deploy-server.sh
    if [[ -f "deploy-server.sh" ]]; then
        sed -i "s/DOMAIN=\"your-domain.com\"/DOMAIN=\"$DOMAIN\"/g" deploy-server.sh
        log_info "✓ deploy-server.sh 配置已更新"
    fi
    
    # 更新setup-ssl.sh
    if [[ -f "setup-ssl.sh" ]]; then
        sed -i "s/DOMAIN=\"your-domain.com\"/DOMAIN=\"$DOMAIN\"/g" setup-ssl.sh
        sed -i "s/EMAIL=\"<EMAIL>\"/EMAIL=\"$EMAIL\"/g" setup-ssl.sh
        log_info "✓ setup-ssl.sh 配置已更新"
    fi
    
    # 更新monitor-site.sh
    if [[ -f "monitor-site.sh" ]]; then
        sed -i "s/DOMAIN=\"your-domain.com\"/DOMAIN=\"$DOMAIN\"/g" monitor-site.sh
        sed -i "s/EMAIL=\"<EMAIL>\"/EMAIL=\"$EMAIL\"/g" monitor-site.sh
        log_info "✓ monitor-site.sh 配置已更新"
    fi
    
    log_info "配置文件更新完成"
}

# 设置脚本权限
set_permissions() {
    log_step "设置脚本权限..."
    
    chmod +x *.sh
    
    log_info "脚本权限设置完成"
}

# 执行部署步骤
deploy_steps() {
    log_step "开始部署流程..."
    
    # 步骤1: 部署服务器环境
    echo ""
    log_step "步骤1/4: 部署服务器环境..."
    if [[ -f "deploy-server.sh" ]]; then
        ./deploy-server.sh
        log_info "✓ 服务器环境部署完成"
    else
        log_error "deploy-server.sh 文件不存在"
        exit 1
    fi
    
    # 步骤2: 上传网站文件
    echo ""
    log_step "步骤2/4: 上传网站文件..."
    if [[ -f "upload-site.sh" ]]; then
        ./upload-site.sh
        log_info "✓ 网站文件上传完成"
    else
        log_error "upload-site.sh 文件不存在"
        exit 1
    fi
    
    # 步骤3: 配置SSL证书
    echo ""
    log_step "步骤3/4: 配置SSL证书..."
    read -p "是否配置SSL证书？(Y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Nn]$ ]]; then
        log_warn "跳过SSL证书配置"
    else
        if [[ -f "setup-ssl.sh" ]]; then
            ./setup-ssl.sh
            log_info "✓ SSL证书配置完成"
        else
            log_error "setup-ssl.sh 文件不存在"
        fi
    fi
    
    # 步骤4: 安装监控
    echo ""
    log_step "步骤4/4: 安装监控系统..."
    read -p "是否安装网站监控？(Y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Nn]$ ]]; then
        log_warn "跳过监控系统安装"
    else
        if [[ -f "monitor-site.sh" ]]; then
            ./monitor-site.sh --install
            log_info "✓ 监控系统安装完成"
        else
            log_error "monitor-site.sh 文件不存在"
        fi
    fi
    
    log_info "部署流程完成"
}

# 验证部署结果
verify_deployment() {
    log_step "验证部署结果..."
    
    # 检查Nginx状态
    if systemctl is-active --quiet nginx; then
        log_info "✓ Nginx服务运行正常"
    else
        log_error "✗ Nginx服务未运行"
    fi
    
    # 检查网站文件
    if [[ -f "/var/www/portfolio/index.html" ]]; then
        log_info "✓ 网站文件部署成功"
    else
        log_error "✗ 网站文件部署失败"
    fi
    
    # 检查HTTP访问
    if curl -s "http://$DOMAIN" > /dev/null; then
        log_info "✓ HTTP访问正常"
    else
        log_warn "✗ HTTP访问可能有问题"
    fi
    
    # 检查HTTPS访问
    if curl -s "https://$DOMAIN" > /dev/null; then
        log_info "✓ HTTPS访问正常"
    else
        log_warn "✗ HTTPS访问可能有问题（如果未配置SSL则正常）"
    fi
    
    log_info "部署验证完成"
}

# 显示部署结果
show_results() {
    echo ""
    echo -e "${GREEN}========================================================"
    echo "                 部署完成！"
    echo "========================================================${NC}"
    echo ""
    echo "网站信息:"
    echo "- 域名: $DOMAIN"
    echo "- HTTP访问: http://$DOMAIN"
    echo "- HTTPS访问: https://$DOMAIN"
    echo ""
    echo "管理工具:"
    echo "- 网站管理: sudo ./manage-site.sh"
    echo "- 监控状态: sudo ./monitor-site.sh --status"
    echo "- 更新网站: sudo ./upload-site.sh"
    echo ""
    echo "重要目录:"
    echo "- 网站根目录: /var/www/portfolio"
    echo "- 备份目录: /var/backups/portfolio"
    echo "- 日志目录: /var/log/portfolio"
    echo ""
    echo "后续维护:"
    echo "1. 定期检查网站状态"
    echo "2. 及时更新网站内容"
    echo "3. 关注SSL证书有效期"
    echo "4. 定期备份重要数据"
    echo ""
    echo -e "${YELLOW}注意: 请确保域名已正确解析到此服务器IP${NC}"
    echo ""
}

# 主函数
main() {
    show_banner
    
    log_info "开始一键部署个人网站..."
    
    check_requirements
    collect_config
    update_config_files
    set_permissions
    deploy_steps
    verify_deployment
    show_results
    
    log_info "一键部署完成！"
}

# 显示帮助信息
show_help() {
    echo "个人网站一键部署脚本"
    echo ""
    echo "使用方法:"
    echo "  $0              # 开始一键部署"
    echo "  $0 --help       # 显示帮助信息"
    echo ""
    echo "部署前准备:"
    echo "1. 确保服务器有root权限"
    echo "2. 确保域名已解析到服务器IP"
    echo "3. 确保80和443端口开放"
    echo "4. 准备好邮箱地址用于SSL证书"
    echo ""
    echo "部署内容:"
    echo "- Nginx Web服务器"
    echo "- 个人网站文件"
    echo "- SSL证书 (Let's Encrypt)"
    echo "- 网站监控系统"
    echo "- 自动化管理工具"
}

# 处理命令行参数
case "${1:-}" in
    --help|-h)
        show_help
        exit 0
        ;;
    "")
        main "$@"
        ;;
    *)
        log_error "未知参数: $1"
        show_help
        exit 1
        ;;
esac
