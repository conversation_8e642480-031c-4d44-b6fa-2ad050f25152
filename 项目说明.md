# 产品经理简历导航页 - 项目说明

## 🎯 项目目标

创建一个专业的单页导航网站，用于展示产品经理的简历和作品集，可部署到GitHub Pages、Vercel等免费平台。

## ✨ 主要功能

### 1. 个人简介展示
- 专业头像区域（使用文字头像）
- 基本信息：姓名、职位、联系方式
- 个人简介和职业亮点
- 简历PDF下载链接

### 2. 分类作品展示
- **云监控与运维系统**
  - 金山云监控系统（RDMA网络拓扑、告警优化等）
  - 优维科技CMDB系统（配置管理、结构化等）
  - 众邦银行运维平台（蓝鲸自动化运维）

- **能源管理系统**
  - 智能能源管理平台（在线演示）
  - 虚拟电厂、负荷控制、能效分析

- **AI智能体应用**
  - 医疗AI、办公助手、客服AI等
  - 基于大语言模型的多场景应用

### 3. 专业技能展示
- 产品管理核心能力
- AI与新技术应用
- 技术背景与工具
- 职业发展历程

### 4. 联系方式
- 多种联系方式展示
- 专业的联系卡片设计
- 工作地点和合作意向

## 🎨 设计特色

### 视觉设计
- 现代化的渐变色彩搭配
- 卡片式布局，层次分明
- 响应式设计，适配各种设备
- 平滑的动画效果和交互

### 用户体验
- 清晰的信息架构
- 直观的导航和分类
- 快速的文件下载
- 外部链接新窗口打开

### 技术实现
- 纯HTML/CSS/JavaScript
- 无需后端服务器
- 快速加载，SEO友好
- 易于维护和更新

## 📁 文件结构

```
简历展示页面/
├── index.html              # 主页面（GitHub Pages默认）
├── protfolio.html          # 主页面（原始文件）
├── 个人简历-张一钦.pdf      # 完整简历
├── 原型链接合集.docx        # 作品集文档
├── README.md               # 项目说明
├── deploy.md               # 部署指南
├── 项目说明.md             # 本文件
├── vercel.json             # Vercel部署配置
├── init-git.bat            # Git初始化脚本
├── 1. 能源管理系统/         # 项目文档
├── 2.金山云需求集/          # 云监控项目
├── 3.优维科技/              # CMDB项目
└── 4.众邦银行/              # 金融运维项目
```

## 🚀 部署选项

### 1. GitHub Pages（推荐）
- **优势**：免费、稳定、与GitHub集成
- **域名**：username.github.io/repository
- **部署**：推送代码后在设置中启用Pages

### 2. Vercel
- **优势**：部署快速、自动化CI/CD
- **域名**：project.vercel.app
- **部署**：连接GitHub仓库自动部署

### 3. Netlify
- **优势**：功能丰富、表单处理
- **域名**：project.netlify.app
- **部署**：拖拽文件或Git连接

## 📝 使用建议

### 在简历中的应用
1. **简历顶部添加**
   ```
   个人作品集：https://你的域名
   ```

2. **项目经验中引用**
   ```
   详细案例和原型设计请访问：https://你的域名
   ```

3. **技能展示补充**
   ```
   在线演示和文档下载：https://你的域名
   ```

### 面试准备
- 提前准备好各项目的详细介绍
- 确保所有链接都能正常访问
- 准备演示能源管理系统的功能
- 整理AI智能体的应用场景

## 🔧 自定义修改

### 个人信息更新
- 修改姓名、联系方式、职位信息
- 更新个人简介和职业亮点
- 替换简历PDF和作品集文档

### 项目内容调整
- 添加新的项目案例
- 更新项目描述和链接
- 调整项目分类和标签

### 样式定制
- 修改主题色彩（CSS变量）
- 调整布局和间距
- 添加新的动画效果

## 📊 效果预期

### 专业形象提升
- 展示技术与产品的复合背景
- 体现对新技术的敏感度
- 突出实际项目经验

### 求职效果
- 提供详细的作品展示
- 方便HR和面试官了解能力
- 增加面试邀请机会

### 职业发展
- 建立个人品牌
- 扩大行业影响力
- 吸引合作机会

## 🎯 后续优化方向

### 功能增强
- 添加访问统计
- 集成在线聊天
- 增加项目筛选功能
- 添加博客文章链接

### 内容丰富
- 定期更新项目案例
- 添加技术文章链接
- 增加行业观点分享
- 展示学习成果

### 技术升级
- 使用现代前端框架
- 添加PWA功能
- 优化SEO效果
- 提升加载速度

## 📞 技术支持

如有问题或需要帮助，请联系：
- 邮箱：<EMAIL>
- 电话：18062604712

---

**祝您求职顺利！** 🎉
